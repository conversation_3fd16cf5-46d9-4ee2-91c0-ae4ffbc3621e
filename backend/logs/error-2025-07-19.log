2025-07-19 16:03:34:334 [31merror[39m: [31muncaughtException: ⨯ Unable to compile TypeScript:[39m
[31msrc/config/redis.ts(13,23): error TS2769: No overload matches this call.[39m
[31m  Overload 1 of 8, '(options: RedisOptions): Redis', gave the following error.[39m
[31m    Object literal may only specify known properties, and 'retryDelayOnFailover' does not exist in type 'RedisOptions'.[39m
[31m  Overload 2 of 8, '(port: number): Redis', gave the following error.[39m
[31m    Argument of type '{ host: string; port: number; password: string | undefined; db: number; retryDelayOnFailover: number; enableReadyCheck: boolean; maxRetriesPerRequest: number; lazyConnect: boolean; connectTimeout: number; commandTimeout: number; }' is not assignable to parameter of type 'number'.[39m
[31m  Overload 3 of 8, '(path: string): Redis', gave the following error.[39m
[31m    Argument of type '{ host: string; port: number; password: string | undefined; db: number; retryDelayOnFailover: number; enableReadyCheck: boolean; maxRetriesPerRequest: number; lazyConnect: boolean; connectTimeout: number; commandTimeout: number; }' is not assignable to parameter of type 'string'.[39m

[31mTSError: ⨯ Unable to compile TypeScript:[39m
[31msrc/config/redis.ts(13,23): error TS2769: No overload matches this call.[39m
[31m  Overload 1 of 8, '(options: RedisOptions): Redis', gave the following error.[39m
[31m    Object literal may only specify known properties, and 'retryDelayOnFailover' does not exist in type 'RedisOptions'.[39m
[31m  Overload 2 of 8, '(port: number): Redis', gave the following error.[39m
[31m    Argument of type '{ host: string; port: number; password: string | undefined; db: number; retryDelayOnFailover: number; enableReadyCheck: boolean; maxRetriesPerRequest: number; lazyConnect: boolean; connectTimeout: number; commandTimeout: number; }' is not assignable to parameter of type 'number'.[39m
[31m  Overload 3 of 8, '(path: string): Redis', gave the following error.[39m
[31m    Argument of type '{ host: string; port: number; password: string | undefined; db: number; retryDelayOnFailover: number; enableReadyCheck: boolean; maxRetriesPerRequest: number; lazyConnect: boolean; connectTimeout: number; commandTimeout: number; }' is not assignable to parameter of type 'string'.[39m

[31m    at createTSError (/Volumes/acasis/yoghurt/node_modules/ts-node/src/index.ts:859:12)[39m
[31m    at reportTSError (/Volumes/acasis/yoghurt/node_modules/ts-node/src/index.ts:863:19)[39m
[31m    at getOutput (/Volumes/acasis/yoghurt/node_modules/ts-node/src/index.ts:1077:36)[39m
[31m    at Object.compile (/Volumes/acasis/yoghurt/node_modules/ts-node/src/index.ts:1433:41)[39m
[31m    at Module.m._compile (/Volumes/acasis/yoghurt/node_modules/ts-node/src/index.ts:1617:30)[39m
[31m    at Module._extensions..js (node:internal/modules/cjs/loader:1699:10)[39m
[31m    at Object.require.extensions.<computed> [as .ts] (/Volumes/acasis/yoghurt/node_modules/ts-node/src/index.ts:1621:12)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1313:32)[39m
[31m    at Function.Module._load (node:internal/modules/cjs/loader:1123:12)[39m
[31m    at TracingChannel.traceSync (node:diagnostics_channel:322:14)[39m
2025-07-19 16:04:20:420 [31merror[39m: [31muncaughtException: ⨯ Unable to compile TypeScript:[39m
[31msrc/config/redis.ts(13,23): error TS2769: No overload matches this call.[39m
[31m  Overload 1 of 8, '(options: RedisOptions): Redis', gave the following error.[39m
[31m    Object literal may only specify known properties, and 'retryDelayOnFailover' does not exist in type 'RedisOptions'.[39m
[31m  Overload 2 of 8, '(port: number): Redis', gave the following error.[39m
[31m    Argument of type '{ host: string; port: number; password: string | undefined; db: number; retryDelayOnFailover: number; enableReadyCheck: boolean; maxRetriesPerRequest: number; lazyConnect: boolean; connectTimeout: number; commandTimeout: number; }' is not assignable to parameter of type 'number'.[39m
[31m  Overload 3 of 8, '(path: string): Redis', gave the following error.[39m
[31m    Argument of type '{ host: string; port: number; password: string | undefined; db: number; retryDelayOnFailover: number; enableReadyCheck: boolean; maxRetriesPerRequest: number; lazyConnect: boolean; connectTimeout: number; commandTimeout: number; }' is not assignable to parameter of type 'string'.[39m

[31mTSError: ⨯ Unable to compile TypeScript:[39m
[31msrc/config/redis.ts(13,23): error TS2769: No overload matches this call.[39m
[31m  Overload 1 of 8, '(options: RedisOptions): Redis', gave the following error.[39m
[31m    Object literal may only specify known properties, and 'retryDelayOnFailover' does not exist in type 'RedisOptions'.[39m
[31m  Overload 2 of 8, '(port: number): Redis', gave the following error.[39m
[31m    Argument of type '{ host: string; port: number; password: string | undefined; db: number; retryDelayOnFailover: number; enableReadyCheck: boolean; maxRetriesPerRequest: number; lazyConnect: boolean; connectTimeout: number; commandTimeout: number; }' is not assignable to parameter of type 'number'.[39m
[31m  Overload 3 of 8, '(path: string): Redis', gave the following error.[39m
[31m    Argument of type '{ host: string; port: number; password: string | undefined; db: number; retryDelayOnFailover: number; enableReadyCheck: boolean; maxRetriesPerRequest: number; lazyConnect: boolean; connectTimeout: number; commandTimeout: number; }' is not assignable to parameter of type 'string'.[39m

[31m    at createTSError (/Volumes/acasis/yoghurt/node_modules/ts-node/src/index.ts:859:12)[39m
[31m    at reportTSError (/Volumes/acasis/yoghurt/node_modules/ts-node/src/index.ts:863:19)[39m
[31m    at getOutput (/Volumes/acasis/yoghurt/node_modules/ts-node/src/index.ts:1077:36)[39m
[31m    at Object.compile (/Volumes/acasis/yoghurt/node_modules/ts-node/src/index.ts:1433:41)[39m
[31m    at Module.m._compile (/Volumes/acasis/yoghurt/node_modules/ts-node/src/index.ts:1617:30)[39m
[31m    at Module._extensions..js (node:internal/modules/cjs/loader:1699:10)[39m
[31m    at Object.require.extensions.<computed> [as .ts] (/Volumes/acasis/yoghurt/node_modules/ts-node/src/index.ts:1621:12)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1313:32)[39m
[31m    at Function.Module._load (node:internal/modules/cjs/loader:1123:12)[39m
[31m    at TracingChannel.traceSync (node:diagnostics_channel:322:14)[39m
2025-07-19 16:04:27:427 [31merror[39m: [31muncaughtException: ⨯ Unable to compile TypeScript:[39m
[31m[96msrc/config/redis.ts[0m:[93m13[0m:[93m23[0m - [91merror[0m[90m TS2769: [0mNo overload matches this call.[39m
[31m  Overload 1 of 8, '(options: RedisOptions): Redis', gave the following error.[39m
[31m    Object literal may only specify known properties, and 'retryDelayOnFailover' does not exist in type 'RedisOptions'.[39m
[31m  Overload 2 of 8, '(port: number): Redis', gave the following error.[39m
[31m    Argument of type '{ host: string; port: number; password: string | undefined; db: number; retryDelayOnFailover: number; enableReadyCheck: boolean; maxRetriesPerRequest: number; lazyConnect: boolean; connectTimeout: number; commandTimeout: number; }' is not assignable to parameter of type 'number'.[39m
[31m  Overload 3 of 8, '(path: string): Redis', gave the following error.[39m
[31m    Argument of type '{ host: string; port: number; password: string | undefined; db: number; retryDelayOnFailover: number; enableReadyCheck: boolean; maxRetriesPerRequest: number; lazyConnect: boolean; connectTimeout: number; commandTimeout: number; }' is not assignable to parameter of type 'string'.[39m

[31m[7m13[0m     redisClient = new Redis({[39m
[31m[7m  [0m [91m                      ~~~~~[0m[39m


[31mTSError: ⨯ Unable to compile TypeScript:[39m
[31m[96msrc/config/redis.ts[0m:[93m13[0m:[93m23[0m - [91merror[0m[90m TS2769: [0mNo overload matches this call.[39m
[31m  Overload 1 of 8, '(options: RedisOptions): Redis', gave the following error.[39m
[31m    Object literal may only specify known properties, and 'retryDelayOnFailover' does not exist in type 'RedisOptions'.[39m
[31m  Overload 2 of 8, '(port: number): Redis', gave the following error.[39m
[31m    Argument of type '{ host: string; port: number; password: string | undefined; db: number; retryDelayOnFailover: number; enableReadyCheck: boolean; maxRetriesPerRequest: number; lazyConnect: boolean; connectTimeout: number; commandTimeout: number; }' is not assignable to parameter of type 'number'.[39m
[31m  Overload 3 of 8, '(path: string): Redis', gave the following error.[39m
[31m    Argument of type '{ host: string; port: number; password: string | undefined; db: number; retryDelayOnFailover: number; enableReadyCheck: boolean; maxRetriesPerRequest: number; lazyConnect: boolean; connectTimeout: number; commandTimeout: number; }' is not assignable to parameter of type 'string'.[39m

[31m[7m13[0m     redisClient = new Redis({[39m
[31m[7m  [0m [91m                      ~~~~~[0m[39m


[31m    at createTSError (/Volumes/acasis/yoghurt/node_modules/ts-node/src/index.ts:859:12)[39m
[31m    at reportTSError (/Volumes/acasis/yoghurt/node_modules/ts-node/src/index.ts:863:19)[39m
[31m    at getOutput (/Volumes/acasis/yoghurt/node_modules/ts-node/src/index.ts:1077:36)[39m
[31m    at Object.compile (/Volumes/acasis/yoghurt/node_modules/ts-node/src/index.ts:1433:41)[39m
[31m    at Module.m._compile (/Volumes/acasis/yoghurt/node_modules/ts-node/src/index.ts:1617:30)[39m
[31m    at Module._extensions..js (node:internal/modules/cjs/loader:1699:10)[39m
[31m    at Object.require.extensions.<computed> [as .ts] (/Volumes/acasis/yoghurt/node_modules/ts-node/src/index.ts:1621:12)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1313:32)[39m
[31m    at Function.Module._load (node:internal/modules/cjs/loader:1123:12)[39m
[31m    at TracingChannel.traceSync (node:diagnostics_channel:322:14)[39m
