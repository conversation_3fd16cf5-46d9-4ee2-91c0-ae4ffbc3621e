{"name": "yoghurt-ai-qc-backend", "version": "1.0.0", "description": "Yoghurt AI QC Backend API Service", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "type-check": "tsc --noEmit", "migrate": "npx prisma migrate dev", "migrate:prod": "npx prisma migrate deploy", "db:generate": "npx prisma generate", "db:studio": "npx prisma studio", "db:seed": "ts-node src/scripts/seed.ts", "db:reset": "npx prisma migrate reset", "format": "prettier --write \"src/**/*.{ts,js,json}\"", "format:check": "prettier --check \"src/**/*.{ts,js,json}\""}, "dependencies": {"@prisma/client": "^5.6.0", "axios": "^1.6.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dayjs": "^1.11.10", "dotenv": "^16.3.1", "express": "^4.18.2", "express-prom-bundle": "^7.0.0", "express-rate-limit": "^7.1.5", "express-slow-down": "^2.0.1", "express-validator": "^7.0.1", "helmet": "^7.1.0", "ioredis": "^5.3.2", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "pg": "^8.11.3", "prisma": "^5.6.0", "prom-client": "^15.0.0", "redis": "^4.6.10", "sharp": "^0.32.6", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "uuid": "^9.0.1", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/lodash": "^4.14.202", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.9.0", "@types/node-cron": "^3.0.11", "@types/pg": "^8.10.7", "@types/supertest": "^2.0.16", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.11.0", "@typescript-eslint/parser": "^6.11.0", "eslint": "^8.54.0", "jest": "^29.7.0", "nodemon": "^3.0.1", "prettier": "^3.1.0", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "keywords": ["nodejs", "express", "typescript", "api", "yogurt", "quality-control", "ai"], "author": "Your Name", "license": "MIT"}