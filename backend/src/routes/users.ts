import { Router, Request, Response } from 'express'
import { async<PERSON>and<PERSON> } from '../middleware/errorHandler'

const router = Router()

/**
 * @swagger
 * /api/users:
 *   get:
 *     summary: Get all users
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Users retrieved successfully
 */
router.get('/', asyncHandler(async (req: Request, res: Response) => {
  res.json({
    success: true,
    data: {
      users: [],
      message: 'Users endpoint - implementation pending',
    },
  })
}))

export default router
