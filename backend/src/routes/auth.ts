import { Router, Request, Response } from 'express'
import bcrypt from 'bcryptjs'
import { body, validationResult } from 'express-validator'
import { getDatabase } from '../config/database'
import { generateToken, generateRefreshToken, verifyToken } from '../middleware/auth'
import { AppError, asyncHand<PERSON> } from '../middleware/errorHandler'
import { logger } from '../utils/logger'

const router = Router()

/**
 * @swagger
 * components:
 *   schemas:
 *     LoginRequest:
 *       type: object
 *       required:
 *         - email
 *         - password
 *       properties:
 *         email:
 *           type: string
 *           format: email
 *         password:
 *           type: string
 *           minLength: 6
 *     LoginResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *         data:
 *           type: object
 *           properties:
 *             user:
 *               type: object
 *               properties:
 *                 id:
 *                   type: string
 *                 email:
 *                   type: string
 *                 role:
 *                   type: string
 *             token:
 *               type: string
 *             refreshToken:
 *               type: string
 */

/**
 * @swagger
 * /api/auth/login:
 *   post:
 *     summary: User login
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/LoginRequest'
 *     responses:
 *       200:
 *         description: Login successful
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/LoginResponse'
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Invalid credentials
 */
router.post('/login', [
  body('email').isEmail().normalizeEmail(),
  body('password').isLength({ min: 6 }),
], asyncHandler(async (req: Request, res: Response) => {
  const errors = validationResult(req)
  if (!errors.isEmpty()) {
    throw new AppError('Invalid input data', 400)
  }

  const { email, password } = req.body
  const db = getDatabase()

  // 查找用户
  const userQuery = 'SELECT id, email, password_hash, role, created_at FROM users WHERE email = $1'
  const userResult = await db.query(userQuery, [email])

  if (userResult.rows.length === 0) {
    throw new AppError('Invalid credentials', 401)
  }

  const user = userResult.rows[0]

  // 验证密码
  const isPasswordValid = await bcrypt.compare(password, user.password_hash)
  if (!isPasswordValid) {
    throw new AppError('Invalid credentials', 401)
  }

  // 生成 tokens
  const tokenPayload = {
    id: user.id,
    email: user.email,
    role: user.role,
  }

  const token = generateToken(tokenPayload)
  const refreshToken = generateRefreshToken(tokenPayload)

  // 记录登录
  logger.info(`User logged in: ${email}`)

  res.json({
    success: true,
    data: {
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
      },
      token,
      refreshToken,
    },
  })
}))

/**
 * @swagger
 * /api/auth/refresh:
 *   post:
 *     summary: Refresh access token
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - refreshToken
 *             properties:
 *               refreshToken:
 *                 type: string
 *     responses:
 *       200:
 *         description: Token refreshed successfully
 *       401:
 *         description: Invalid refresh token
 */
router.post('/refresh', asyncHandler(async (req: Request, res: Response) => {
  const { refreshToken } = req.body

  if (!refreshToken) {
    throw new AppError('Refresh token is required', 401)
  }

  try {
    const decoded = verifyToken(refreshToken)
    
    // 生成新的访问令牌
    const newToken = generateToken({
      id: decoded.id,
      email: decoded.email,
      role: decoded.role,
    })

    res.json({
      success: true,
      data: {
        token: newToken,
      },
    })
  } catch (error) {
    throw new AppError('Invalid refresh token', 401)
  }
}))

/**
 * @swagger
 * /api/auth/me:
 *   get:
 *     summary: Get current user info
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User info retrieved successfully
 *       401:
 *         description: Unauthorized
 */
router.get('/me', asyncHandler(async (req: Request, res: Response) => {
  // 这个路由需要在有认证中间件的情况下使用
  // 暂时返回一个基本响应
  res.json({
    success: true,
    data: {
      message: 'Authentication endpoint - requires auth middleware',
    },
  })
}))

export default router
