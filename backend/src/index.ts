import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import compression from 'compression'
import morgan from 'morgan'
import rateLimit from 'express-rate-limit'
import slowDown from 'express-slow-down'
import swaggerJsdoc from 'swagger-jsdoc'
import swaggerUi from 'swagger-ui-express'
import promClient from 'prom-client'
import promBundle from 'express-prom-bundle'

import { config } from './config'
import { logger } from './utils/logger'
import { connectDatabase } from './config/database'
import { connectRedis } from './config/redis'
import { errorHandler } from './middleware/errorHandler'
import { notFoundHandler } from './middleware/notFoundHandler'
import { authMiddleware } from './middleware/auth'

// 路由导入
import authRoutes from './routes/auth'
import userRoutes from './routes/users'
import recipeRoutes from './routes/recipes'
import batchRoutes from './routes/batches'
import analysisRoutes from './routes/analysis'
import reportRoutes from './routes/reports'
import healthRoutes from './routes/health'

const app = express()

// Prometheus 指标收集
const collectDefaultMetrics = promClient.collectDefaultMetrics
// 清除已有的指标注册
promClient.register.clear()
collectDefaultMetrics()

// Swagger 配置
const swaggerOptions = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Yogurt AI QC API',
      version: '1.0.0',
      description: 'AI-powered yogurt quality control system API',
    },
    servers: [
      {
        url: `http://localhost:${config.port}`,
        description: 'Development server',
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
        },
      },
    },
  },
  apis: ['./src/routes/*.ts'], // 路径到包含 OpenAPI 定义的文件
}

const specs = swaggerJsdoc(swaggerOptions)

// 基础中间件
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}))

app.use(cors({
  origin: config.security.corsOrigin.split(','),
  credentials: config.security.corsCredentials,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
}))

app.use(compression())
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))

// 日志中间件
app.use(morgan('combined', {
  stream: {
    write: (message: string) => logger.info(message.trim())
  }
}))

// 速率限制
const limiter = rateLimit({
  windowMs: config.security.rateLimitWindow * 60 * 1000, // 15 分钟
  max: config.security.rateLimitMaxRequests, // 限制每个IP最多100个请求
  message: {
    error: 'Too many requests from this IP, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
})

const speedLimiter = slowDown({
  windowMs: 15 * 60 * 1000, // 15 分钟
  delayAfter: 50, // 允许前50个请求正常速度
  delayMs: () => 500, // 之后每个请求延迟500ms
  validate: { delayMs: false }, // 禁用警告
})

app.use('/api/', limiter)
app.use('/api/', speedLimiter)

// Prometheus 指标中间件
const metricsMiddleware = promBundle({
  includeMethod: true,
  includePath: true,
  includeStatusCode: true,
  includeUp: true,
  customLabels: { project_name: 'yoghurt-ai-qc' },
  autoregister: false, // 禁用自动注册
})
app.use(metricsMiddleware)

// API 文档
app.use('/api/docs', swaggerUi.serve, swaggerUi.setup(specs))

// 健康检查路由（不需要认证）
app.use('/health', healthRoutes)
app.use('/metrics', (req, res) => {
  res.set('Content-Type', promClient.register.contentType)
  res.end(promClient.register.metrics())
})

// API 路由
app.use('/api/auth', authRoutes)
app.use('/api/users', authMiddleware, userRoutes)
app.use('/api/recipes', authMiddleware, recipeRoutes)
app.use('/api/batches', authMiddleware, batchRoutes)
app.use('/api/analysis', authMiddleware, analysisRoutes)
app.use('/api/reports', authMiddleware, reportRoutes)

// 根路径
app.get('/', (req, res) => {
  res.json({
    name: 'Yogurt AI QC API',
    version: '1.0.0',
    status: 'running',
    timestamp: new Date().toISOString(),
    docs: '/api/docs',
    health: '/health',
  })
})

// 错误处理中间件
app.use(notFoundHandler)
app.use(errorHandler)

// 启动服务器
const startServer = async () => {
  try {
    // 连接数据库
    await connectDatabase()
    logger.info('Database connected successfully')

    // 连接 Redis
    await connectRedis()
    logger.info('Redis connected successfully')

    // 启动服务器
    const server = app.listen(config.port, () => {
      logger.info(`Server is running on port ${config.port}`)
      logger.info(`API Documentation: http://localhost:${config.port}/api/docs`)
      logger.info(`Health Check: http://localhost:${config.port}/health`)
      logger.info(`Metrics: http://localhost:${config.port}/metrics`)
    })

    // 优雅关闭
    const gracefulShutdown = (signal: string) => {
      logger.info(`Received ${signal}. Starting graceful shutdown...`)
      
      server.close(() => {
        logger.info('HTTP server closed')
        
        // 关闭数据库连接
        // 这里可以添加数据库连接关闭逻辑
        
        logger.info('Graceful shutdown completed')
        process.exit(0)
      })

      // 强制关闭超时
      setTimeout(() => {
        logger.error('Forced shutdown due to timeout')
        process.exit(1)
      }, 10000)
    }

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'))
    process.on('SIGINT', () => gracefulShutdown('SIGINT'))

  } catch (error) {
    logger.error('Failed to start server:', error)
    process.exit(1)
  }
}

// 未捕获的异常处理
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error)
  process.exit(1)
})

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason)
  process.exit(1)
})

startServer()
