"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var React = _interopRequireWildcard(require("react"));
/* tslint:disable: no-object-literal-type-assertion */

// We will never use default, here only to fix TypeScript warning
var MentionsContext = /*#__PURE__*/React.createContext(null);
var _default = exports.default = MentionsContext;