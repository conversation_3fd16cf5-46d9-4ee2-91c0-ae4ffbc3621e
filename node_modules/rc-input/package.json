{"name": "rc-input", "version": "1.8.0", "description": "React input component", "keywords": ["react", "react-component", "react-input", "input", "antd", "ant-design"], "main": "./lib/index", "module": "./es/index", "files": ["assets/*.css", "assets/*.less", "es", "lib"], "homepage": "https://github.com/react-component/input", "repository": {"type": "git", "url": "**************:react-component/input.git"}, "bugs": {"url": "http://github.com/react-component/input/issues"}, "license": "MIT", "scripts": {"start": "dumi dev", "docs:build": "dumi build", "docs:deploy": "gh-pages -d .doc", "compile": "father build && lessc assets/index.less assets/index.css", "gh-pages": "GH_PAGES=1 npm run docs:build && npm run docs:deploy", "prepublishOnly": "npm run compile && np --yolo --no-publish --branch antd-5.x", "postpublish": "npm run gh-pages", "lint": "eslint src/ --ext .ts,.tsx,.jsx,.js,.md", "prettier": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "pretty-quick": "pretty-quick", "lint-staged": "lint-staged", "test": "rc-test", "coverage": "rc-test --coverage", "prepare": "husky install"}, "dependencies": {"@babel/runtime": "^7.11.1", "classnames": "^2.2.1", "rc-util": "^5.18.1"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.0.0-beta", "@types/classnames": "^2.2.9", "@types/jest": "^29.5.14", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.1", "@umijs/fabric": "^4.0.0", "coveralls": "^3.0.6", "cross-env": "^7.0.2", "dumi": "^2.1.14", "eslint": "^8.0.0", "father": "^4.3.7", "gh-pages": "^6.2.0", "husky": "^9.1.7", "less": "^4.2.1", "lint-staged": "^15.2.11", "np": "^10.1.0", "prettier": "^3.4.2", "pretty-quick": "^4.0.0", "rc-test": "^7.0.15", "react": "^18.0.0", "react-dom": "^18.0.0", "typescript": "^5.7.2"}, "peerDependencies": {"react": ">=16.0.0", "react-dom": ">=16.0.0"}, "husky": {"hooks": {"pre-commit": "pretty-quick --staged"}}, "lint-staged": {"**/*.{js,jsx,tsx,ts,md,json}": ["prettier --write"]}, "cnpm": {"mode": "npm"}, "tnpm": {"mode": "npm"}}