export declare const placements: {
    topLeft: {
        points: string[];
        overflow: {
            adjustX: number;
            adjustY: number;
        };
    };
    topRight: {
        points: string[];
        overflow: {
            adjustX: number;
            adjustY: number;
        };
    };
    bottomLeft: {
        points: string[];
        overflow: {
            adjustX: number;
            adjustY: number;
        };
    };
    bottomRight: {
        points: string[];
        overflow: {
            adjustX: number;
            adjustY: number;
        };
    };
    leftTop: {
        points: string[];
        overflow: {
            adjustX: number;
            adjustY: number;
        };
    };
    leftBottom: {
        points: string[];
        overflow: {
            adjustX: number;
            adjustY: number;
        };
    };
    rightTop: {
        points: string[];
        overflow: {
            adjustX: number;
            adjustY: number;
        };
    };
    rightBottom: {
        points: string[];
        overflow: {
            adjustX: number;
            adjustY: number;
        };
    };
};
export declare const placementsRtl: {
    topLeft: {
        points: string[];
        overflow: {
            adjustX: number;
            adjustY: number;
        };
    };
    topRight: {
        points: string[];
        overflow: {
            adjustX: number;
            adjustY: number;
        };
    };
    bottomLeft: {
        points: string[];
        overflow: {
            adjustX: number;
            adjustY: number;
        };
    };
    bottomRight: {
        points: string[];
        overflow: {
            adjustX: number;
            adjustY: number;
        };
    };
    rightTop: {
        points: string[];
        overflow: {
            adjustX: number;
            adjustY: number;
        };
    };
    rightBottom: {
        points: string[];
        overflow: {
            adjustX: number;
            adjustY: number;
        };
    };
    leftTop: {
        points: string[];
        overflow: {
            adjustX: number;
            adjustY: number;
        };
    };
    leftBottom: {
        points: string[];
        overflow: {
            adjustX: number;
            adjustY: number;
        };
    };
};
export default placements;
